"use client";

import React, { useState, useRef } from "react";
import { uploadAvatar } from "@/services/profileService";
import { validateAvatarFile } from "@/utils/errorHandler";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Upload, Camera, AlertCircle, CheckCircle } from "lucide-react";

interface AvatarUploadProps {
  currentAvatar?: string | null;
  username: string;
  nickname?: string | null;
  onUploadSuccess?: (newAvatarUrl: string) => void;
}

export function AvatarUpload({
  currentAvatar,
  username,
  nickname,
  onUploadSuccess,
}: AvatarUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 重置状态
    setError(null);
    setSuccess(null);

    // 验证文件
    const validation = validateAvatarFile(file);
    if (!validation.valid) {
      setError(validation.error || "文件验证失败");
      return;
    }

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // 上传文件
    handleUpload(file);
  };

  const handleUpload = async (file: File) => {
    try {
      setUploading(true);
      setUploadProgress(0);
      setError(null);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = await uploadAvatar({ file });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success) {
        setSuccess(result.message || "头像上传成功");
        setPreviewUrl(null);

        // 通知父组件上传成功
        if (onUploadSuccess && result.data?.avatar_url) {
          onUploadSuccess(result.data.avatar_url);
        }

        // 清除成功消息
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.message || "头像上传失败");
      }
    } catch {
      setError("网络错误，请稍后重试");
    } finally {
      setUploading(false);
      setUploadProgress(0);

      // 清除文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const displayName = nickname || username;
  const avatarSrc = previewUrl || currentAvatar;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">头像设置</CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 头像预览 */}
        <div className="flex justify-center">
          <div className="relative">
            <Avatar className="h-24 w-24">
              <AvatarImage src={avatarSrc || undefined} alt={displayName} />
              <AvatarFallback className="text-2xl">
                {displayName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>

            {previewUrl && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">预览</span>
              </div>
            )}
          </div>
        </div>

        {/* 上传按钮 */}
        <div className="text-center">
          <Button
            onClick={handleButtonClick}
            disabled={uploading}
            variant="outline"
            className="w-full"
          >
            {uploading ? (
              <>
                <Upload className="h-4 w-4 mr-2 animate-spin" />
                上传中...
              </>
            ) : (
              <>
                <Camera className="h-4 w-4 mr-2" />
                选择头像
              </>
            )}
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            onChange={handleFileSelect}
            className="hidden"
            aria-label="选择头像文件"
            title="选择头像文件"
          />
        </div>

        {/* 上传进度 */}
        {uploading && (
          <div className="space-y-2">
            <Progress value={uploadProgress} className="w-full" />
            <p className="text-sm text-center text-muted-foreground">
              上传进度: {uploadProgress}%
            </p>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* 成功提示 */}
        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              {success}
            </AlertDescription>
          </Alert>
        )}

        {/* 提示信息 */}
        <div className="text-xs text-muted-foreground text-center space-y-1">
          <p>支持 JPG、PNG、GIF 格式</p>
          <p>文件大小不超过 2MB</p>
          <p>建议尺寸 200x200 像素</p>
        </div>
      </CardContent>
    </Card>
  );
}
